import matplotlib.pyplot as plt
import numpy as np

bands = []
kpoints = []

with open('check.out') as f:
    for line in f:
        if 'freqs:' in line:
            items = line.strip().split(',')
            # items[6:] 可能有如 ' band 1'
            freqs = []
            for x in items[6:]:
                try:
                    freqs.append(float(x))
                except ValueError:
                    continue
            if len(freqs) > 0:
                bands.append(freqs)
                kpoints.append(len(kpoints))

bands = np.array(bands).T

plt.figure(figsize=(7,5))
for b in bands:
    plt.plot(kpoints, b, 'b-')
plt.xlabel('k-point index')
plt.ylabel('Frequency (c/a)')
plt.tight_layout()
plt.savefig('mpb_band.png', dpi=150)
plt.show()