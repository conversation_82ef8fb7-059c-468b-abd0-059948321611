# FCC 光子晶体带结构计算 Web 服务

基于 MIT Photonic Bands (MPB) 的 FCC 光子晶体能带结构在线计算工具。

## 项目结构

```
.
├── fcc_app.py          # 主 Flask 应用
├── mpb_runner.py       # MPB 计算运行器
├── band_plotter.py     # 能带绘图模块
├── config.py           # 配置文件
├── templates/
│   └── index.html      # 前端模板
├── uploads/            # 临时 CTL 文件目录
├── results/            # 结果图片目录
└── README.md           # 项目说明
```

## 功能特性

- **Web 界面**: 现代化的响应式 Web 界面
- **参数配置**: 支持配置介质球半径、介电常数、能带数和网格分辨率
- **实时计算**: 基于 MPB 的实时光子晶体能带结构计算
- **结果可视化**: 自动生成高质量的能带结构图
- **文件管理**: 自动清理临时文件，避免磁盘空间浪费

## 技术栈

- **后端**: Flask (Python)
- **计算引擎**: MIT Photonic Bands (MPB)
- **绘图**: Matplotlib
- **前端**: HTML5 + CSS3 (响应式设计)

## 安装依赖

```bash
pip install flask matplotlib numpy jinja2
```

确保系统已安装 MPB:
```bash
# Ubuntu/Debian
sudo apt-get install mpb

# 或从源码编译安装
```

## 运行应用

```bash
python fcc_app.py
```

访问 http://localhost:5001 使用 Web 界面。

## 参数说明

### 介质球半径 (r)
- 范围: 0.05 - 0.49
- 默认值: 0.25
- 说明: FCC 晶格中介质球的半径

### 介电常数 (ε)
- 范围: 1.0 - 50.0
- 默认值: 11.56
- 说明: 介质球的介电常数

### 计算能带数
- 范围: 2 - 20
- 默认值: 5
- 说明: 计算的能带数量

### 网格分辨率
- 选项: 8, 16, 24, 32, 48, 64
- 默认值: 16
- 说明: MPB 计算使用的网格分辨率

## FCC 晶格结构

本项目使用的是面心立方 (FCC) 晶格结构，包含两个介质球：
- 位置 1: (0.125, 0.125, 0.125)
- 位置 2: (-0.125, -0.125, -0.125)

## 高对称点路径

计算沿以下高对称点路径进行：
X → U → L → Gamma → X → W → K

## 输出文件

- **CTL 文件**: 临时生成的 MPB 输入文件 (自动清理)
- **输出文件**: MPB 计算的标准输出 (自动清理)
- **PNG 图片**: 最终的能带结构图 (保存在 results/ 目录)

## 模块说明

### config.py
包含所有配置参数，包括文件路径、参数范围、默认值等。

### mpb_runner.py
负责生成 CTL 文件和运行 MPB 计算，将输出重定向到文件。

### band_plotter.py
解析 MPB 输出文件并生成能带结构图。

### fcc_app.py
主 Flask 应用，处理 Web 请求和响应。

## 注意事项

1. 确保 MPB 已正确安装并在 PATH 中
2. 计算时间取决于分辨率和能带数，高分辨率可能需要较长时间
3. 临时文件会自动清理，但结果图片会保留
4. 建议在生产环境中使用 WSGI 服务器 (如 Gunicorn)

## 许可证

MIT License
