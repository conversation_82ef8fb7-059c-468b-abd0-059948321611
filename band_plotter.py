#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  能带绘图模块
# ============================================================== #

import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import logging
from config import Config

class BandPlotter:
    """能带绘图类"""
    
    def __init__(self):
        self.symmetry_points = Config.SYMMETRY_POINTS
    
    def parse_mpb_output(self, output_path):
        """解析 MPB 输出文件，提取频率数据"""
        bands = []
        kpoints = []
        
        try:
            with open(output_path, 'r') as f:
                for line in f:
                    if 'freqs:' in line:
                        items = line.strip().split(',')
                        # items[6:] 可能有如 ' band 1'
                        freqs = []
                        for x in items[6:]:
                            try:
                                freqs.append(float(x))
                            except ValueError:
                                continue
                        if len(freqs) > 0:
                            bands.append(freqs)
                            kpoints.append(len(kpoints))
            
            if not bands:
                raise ValueError("未在 MPB 输出中找到任何频率数据")
            
            # 转换为 numpy 数组并转置，使每一行代表一个能带
            bands = np.array(bands).T
            logging.info(f"解析得到 {bands.shape[0]} 个能带，{bands.shape[1]} 个 k 点")
            
            return bands
            
        except Exception as e:
            logging.error(f"解析 MPB 输出失败: {e}")
            raise
    
    def plot_bands(self, freqs, output_path):
        """绘制能带图"""
        try:
            n_bands, n_k = freqs.shape
            k_indices = np.arange(n_k)
            
            plt.figure(figsize=Config.FIGURE_SIZE)
            
            # 绘制每条能带
            colors = plt.cm.tab10(np.linspace(0, 1, min(n_bands, 10)))
            for b in range(n_bands):
                color = colors[b % len(colors)]
                plt.plot(k_indices, freqs[b], '-', color=color, linewidth=2.0, 
                        label=f'Band {b+1}' if n_bands <= 10 else None)
            
            plt.xlabel('k-point index', fontsize=12)
            plt.ylabel('Frequency (c/a)', fontsize=12)
            plt.title('FCC Photonic Crystal Band Structure', fontsize=14, fontweight='bold')
            plt.grid(True, alpha=0.3)
            plt.ylim(0, freqs.max() * 1.05)
            
            # 添加高对称点标签
            n_points = min(len(self.symmetry_points), n_k)
            if n_points > 2:
                # 计算高对称点位置
                tick_positions = np.linspace(0, n_k-1, n_points)
                plt.xticks(tick_positions, self.symmetry_points[:n_points])
                
                # 添加垂直分隔线
                for pos in tick_positions[1:-1]:
                    plt.axvline(x=pos, color='gray', linestyle='--', alpha=0.5, linewidth=0.8)
            
            if n_bands <= 10:
                plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=Config.DPI, bbox_inches='tight')
            plt.close()
            
            logging.info(f"能带图已保存到: {output_path}")
            
        except Exception as e:
            logging.error(f"绘制能带图失败: {e}")
            raise
