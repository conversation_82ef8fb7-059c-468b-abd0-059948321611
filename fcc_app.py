#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  FCC Photonic-crystal band-structure web service (Flask + MPB)
#  - 通过读取 MPB 生成的输出文件绘图
# ============================================================== #

import os
import uuid
import contextlib
import logging
import matplotlib
matplotlib.use('Agg')               # 服务器环境无显示
import matplotlib.pyplot as plt
from flask import Flask, request, render_template, send_file

# 导入自定义模块
from mpb_runner import MPBRunner
from band_plotter import BandPlotter
from config import Config

# ------------------------- 目录与日志 -------------------------
os.makedirs(Config.UPLOAD_FOLDER,  exist_ok=True)
os.makedirs(Config.RESULT_FOLDER, exist_ok=True)
os.makedirs(Config.TEMPLATE_FOLDER, exist_ok=True)

logging.basicConfig(level=logging.INFO,
                    format='[%(asctime)s] %(levelname)s: %(message)s')

# ------------------------- Flask -------------------------
app = Flask(__name__, template_folder=Config.TEMPLATE_FOLDER)

# ------------------------- 工具函数 -------------------------
def validate_params(r, eps, nb, res):
    """验证输入参数"""
    err = []
    if not Config.RADIUS_RANGE[0] <= r <= Config.RADIUS_RANGE[1]: 
        err.append(f"球半径需 {Config.RADIUS_RANGE[0]}-{Config.RADIUS_RANGE[1]}")
    if not Config.EPSILON_RANGE[0] <= eps <= Config.EPSILON_RANGE[1]:
        err.append(f"介电常数需 {Config.EPSILON_RANGE[0]}-{Config.EPSILON_RANGE[1]}")
    if not Config.NUM_BANDS_RANGE[0] <= nb <= Config.NUM_BANDS_RANGE[1]:
        err.append(f"能带数需 {Config.NUM_BANDS_RANGE[0]}-{Config.NUM_BANDS_RANGE[1]}")
    if res not in Config.RESOLUTION_OPTIONS:
        err.append(f"分辨率仅支持 {'/'.join(map(str, Config.RESOLUTION_OPTIONS))}")
    return err

def create_error_png(png_path, msg):
    """创建错误信息图片"""
    plt.figure(figsize=(8, 5))
    plt.text(0.5, 0.5, msg, ha='center', va='center', 
             fontsize=12, wrap=True, 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightcoral"))
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.axis('off')
    plt.title('计算错误', fontsize=14, color='red')
    plt.tight_layout()
    plt.savefig(png_path, dpi=120, bbox_inches='tight')
    plt.close()

# ------------------------- Flask 视图 -------------------------
@app.route('/', methods=['GET', 'POST'])
def index():
    img_url = error = None
    if request.method == 'POST':
        # ---- 读取表单 ----
        try:
            r = float(request.form.get('radius', str(Config.DEFAULT_RADIUS)))
            eps = float(request.form.get('epsilon', str(Config.DEFAULT_EPSILON)))
            nb = int(request.form.get('num_bands', str(Config.DEFAULT_NUM_BANDS)))
            res = int(request.form.get('resolution', str(Config.DEFAULT_RESOLUTION)))
        except Exception:
            return render_template('index.html', error="参数格式错误")
        
        errs = validate_params(r, eps, nb, res)
        if errs:
            return render_template('index.html', error='; '.join(errs))

        # ---- 文件名 ----
        tag = uuid.uuid4().hex[:10]
        ctl_path = os.path.abspath(os.path.join(Config.UPLOAD_FOLDER, f'{tag}.ctl'))
        out_path = os.path.abspath(os.path.join(Config.RESULT_FOLDER, f'{tag}.out'))
        png_path = os.path.abspath(os.path.join(Config.RESULT_FOLDER, f'{tag}.png'))

        try:
            # ---- 运行 MPB 计算 ----
            mpb_runner = MPBRunner()
            mpb_runner.write_ctl_file(ctl_path, r, eps, nb, res)
            logging.info(f'CTL 写入 {ctl_path}')

            # 运行 MPB 并将输出保存到文件
            success = mpb_runner.run_mpb(ctl_path, out_path)
            if not success:
                raise RuntimeError("MPB 计算失败")

            # ---- 解析输出并绘图 ----
            plotter = BandPlotter()
            freqs = plotter.parse_mpb_output(out_path)
            logging.info(f'解析得到频率矩阵 {freqs.shape}')
            
            plotter.plot_bands(freqs, png_path)
            img_url = f'/result/{os.path.basename(png_path)}'

        except Exception as ex:
            error = f'处理错误: {ex}'
            logging.error(error)
            create_error_png(png_path, error)
            img_url = f'/result/{os.path.basename(png_path)}'
        finally:
            # 清理临时文件
            with contextlib.suppress(Exception):
                os.remove(ctl_path)
            with contextlib.suppress(Exception):
                os.remove(out_path)

    return render_template('index.html', img_url=img_url, error=error)

@app.route('/result/<fname>')
def result(fname):
    path = os.path.join(Config.RESULT_FOLDER, fname)
    if not (fname.endswith('.png') and os.path.isfile(path)):
        return '文件不存在', 404
    return send_file(path, mimetype='image/png')

# ------------------------- 启动 -------------------------
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
