<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>FCC 光子晶体带结构计算</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        
        .form-group input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input[type="text"]:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.9em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .alert {
            margin: 20px 0;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .alert-error {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
        }
        
        .result-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .result-container h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .result-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .download-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .download-link:hover {
            background: #218838;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 600px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header, .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FCC 光子晶体带结构计算</h1>
            <p>基于 MIT Photonic Bands (MPB) 的在线计算工具</p>
        </div>
        
        <div class="form-container">
            <form method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label for="radius">介质球半径 (r)</label>
                        <input type="text" id="radius" name="radius" value="0.25">
                        <small>范围: 0.05 - 0.49</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="epsilon">介电常数 (ε)</label>
                        <input type="text" id="epsilon" name="epsilon" value="11.56">
                        <small>范围: 1.0 - 50.0</small>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="num_bands">计算能带数</label>
                        <input type="text" id="num_bands" name="num_bands" value="5">
                        <small>范围: 2 - 20</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="resolution">网格分辨率</label>
                        <input type="text" id="resolution" name="resolution" value="16">
                        <small>选项: 8, 16, 24, 32, 48, 64</small>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">开始计算</button>
                </div>
            </form>

            {% if error %}
                <div class="alert alert-error">
                    <strong>错误:</strong> {{ error }}
                </div>
            {% endif %}

            {% if img_url %}
                <div class="result-container">
                    <h3>计算结果</h3>
                    <img src="{{ img_url }}" alt="FCC 光子晶体能带结构">
                    <br>
                    <a href="{{ img_url }}" download class="download-link">下载图片</a>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
