#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  FCC 光子晶体计算服务启动脚本
# ============================================================== #

import os
import sys
import subprocess
import argparse

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    # 检查 Python 模块
    required_modules = ['flask', 'matplotlib', 'numpy', 'jinja2', 'h5py']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"✗ {module} (缺失)")
    
    if missing_modules:
        print(f"\n缺失的模块: {', '.join(missing_modules)}")
        print("请运行: pip install " + " ".join(missing_modules))
        return False
    
    # 检查 MPB
    try:
        result = subprocess.run(['mpb', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ MPB")
        else:
            print("✗ MPB (未找到或版本错误)")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ MPB (未安装)")
        print("请安装 MIT Photonic Bands (MPB)")
        return False
    
    return True

def setup_directories():
    """设置目录"""
    from config import Config
    
    directories = [Config.UPLOAD_FOLDER, Config.RESULT_FOLDER, Config.TEMPLATE_FOLDER]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ 目录: {directory}")

def main():
    parser = argparse.ArgumentParser(description='FCC 光子晶体计算服务')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5001, help='端口号 (默认: 5001)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--check-only', action='store_true', help='仅检查依赖项')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("FCC 光子晶体带结构计算 Web 服务")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    if args.check_only:
        print("\n依赖项检查完成！")
        return
    
    # 设置目录
    print("\n设置目录...")
    setup_directories()
    
    # 启动应用
    print(f"\n启动服务...")
    print(f"地址: http://{args.host}:{args.port}")
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        # 延迟导入避免循环导入问题
        import fcc_app
        fcc_app.app.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
