#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  模块测试脚本
# ============================================================== #

import os
import tempfile
from mpb_runner import MPBRunner
from band_plotter import BandPlotter
from config import Config

def test_mpb_runner():
    """测试 MPB 运行器"""
    print("测试 MPB 运行器...")
    
    runner = MPBRunner()
    
    # 创建临时 CTL 文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.ctl', delete=False) as f:
        ctl_path = f.name
    
    try:
        # 测试写入 CTL 文件
        success = runner.write_ctl_file(ctl_path, 0.25, 11.56, 5, 16)
        if success:
            print("✓ CTL 文件写入成功")
            
            # 检查文件内容
            with open(ctl_path, 'r') as f:
                content = f.read()
                if 'eps 11.56' in content and 'r 0.25' in content:
                    print("✓ CTL 文件内容正确")
                else:
                    print("✗ CTL 文件内容错误")
        else:
            print("✗ CTL 文件写入失败")
    
    finally:
        # 清理临时文件
        if os.path.exists(ctl_path):
            os.remove(ctl_path)

def test_band_plotter():
    """测试能带绘图器"""
    print("\n测试能带绘图器...")
    
    plotter = BandPlotter()
    
    # 创建模拟的 MPB 输出文件
    mock_output = """
freqs:, 1, 2, 3, 4, 5, 6, 0.1, 0.2, 0.3, 0.4, 0.5
freqs:, 1, 2, 3, 4, 5, 6, 0.15, 0.25, 0.35, 0.45, 0.55
freqs:, 1, 2, 3, 4, 5, 6, 0.2, 0.3, 0.4, 0.5, 0.6
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.out', delete=False) as f:
        f.write(mock_output)
        out_path = f.name
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
        png_path = f.name
    
    try:
        # 测试解析输出
        freqs = plotter.parse_mpb_output(out_path)
        print(f"✓ 解析得到频率矩阵: {freqs.shape}")
        
        # 测试绘图
        plotter.plot_bands(freqs, png_path)
        if os.path.exists(png_path) and os.path.getsize(png_path) > 0:
            print("✓ 能带图生成成功")
        else:
            print("✗ 能带图生成失败")
    
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    finally:
        # 清理临时文件
        for path in [out_path, png_path]:
            if os.path.exists(path):
                os.remove(path)

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    print(f"✓ 上传目录: {Config.UPLOAD_FOLDER}")
    print(f"✓ 结果目录: {Config.RESULT_FOLDER}")
    print(f"✓ 默认半径: {Config.DEFAULT_RADIUS}")
    print(f"✓ 默认介电常数: {Config.DEFAULT_EPSILON}")
    print(f"✓ 高对称点: {Config.SYMMETRY_POINTS}")

if __name__ == '__main__':
    print("=" * 50)
    print("FCC 光子晶体计算模块测试")
    print("=" * 50)
    
    test_config()
    test_mpb_runner()
    test_band_plotter()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
