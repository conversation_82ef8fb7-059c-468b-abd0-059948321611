#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  配置文件
# ============================================================== #

import os

class Config:
    """应用配置类"""
    
    # 目录配置
    UPLOAD_FOLDER = 'uploads'
    RESULT_FOLDER = 'results'
    TEMPLATE_FOLDER = 'templates'
    
    # MPB 配置
    MPB_TIMEOUT = 900  # 15分钟超时
    OMP_NUM_THREADS = '1'
    
    # 参数范围
    RADIUS_RANGE = (0.05, 0.49)
    EPSILON_RANGE = (1.0, 50.0)
    NUM_BANDS_RANGE = (2, 20)
    RESOLUTION_OPTIONS = [8, 16, 24, 32, 48, 64]
    
    # 默认参数 - 更接近真实opal
    DEFAULT_RADIUS = 0.45  # 接近密堆积
    DEFAULT_EPSILON = 11.56
    DEFAULT_NUM_BANDS = 5
    DEFAULT_RESOLUTION = 16
    
    # 绘图配置
    FIGURE_SIZE = (10, 7)
    DPI = 150
    
    # FCC 晶格高对称点
    SYMMETRY_POINTS = ['X', 'U', 'L', 'Gamma', 'X', 'W', 'K']
