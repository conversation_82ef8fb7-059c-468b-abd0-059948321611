#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# ============================================================== #
#  MPB 计算运行器
# ============================================================== #

import os
import subprocess
import logging
from jinja2 import Template
from config import Config

class MPBRunner:
    """MPB 计算运行器类"""
    
    # FCC 晶格 CTL 模板 - 基于您提供的模板
    CTL_TEMPLATE = """
; Dielectric spheres in a diamond (fcc) lattice.  This file is used in
; the "Data Analysis Tutorial" section of the MPB manual.

(set! geometry-lattice (make lattice 
			 (basis-size (sqrt 0.5) (sqrt 0.5) (sqrt 0.5))
			 (basis1 0 1 1)
			 (basis2 1 0 1)
			 (basis3 1 1 0)))

; Corners of the irreducible Brillouin zone for the fcc lattice,
; in a canonical order:
(set! k-points (interpolate 4 (list
			       (vector3 0 0.5 0.5)            ; X
			       (vector3 0 0.625 0.375)        ; U
			       (vector3 0 0.5 0)              ; L
			       (vector3 0 0 0)                ; Gamma
			       (vector3 0 0.5 0.5)            ; X
			       (vector3 0.25 0.75 0.5)        ; W
			       (vector3 0.375 0.75 0.375))))  ; K

; define a couple of parameters (which we can set from the command-line)
(define-param eps {{ epsilon }}) ; the dielectric constant of the spheres
(define-param r {{ radius }})    ; the radius of the spheres

(define diel (make dielectric (epsilon eps)))

; A diamond lattice has two "atoms" per unit cell:
(set! geometry (list (make sphere (center 0.125 0.125 0.125) (radius r) 
			   (material diel))
		     (make sphere (center -0.125 -0.125 -0.125) (radius r) 
			   (material diel))))

; (A simple fcc lattice would have only one sphere/object at the origin.)

(set-param! resolution {{ resolution }}) ; use a {{ resolution }}x{{ resolution }}x{{ resolution }} grid
(set-param! mesh-size 5)
(set-param! num-bands {{ num_bands }})

; run calculation, outputting electric-field energy density at the U point:
(run (output-at-kpoint (vector3 0 0.625 0.375) output-dpwr))
"""
    
    def __init__(self):
        self.template = Template(self.CTL_TEMPLATE)
    
    def write_ctl_file(self, ctl_path, radius, epsilon, num_bands, resolution):
        """写入 CTL 文件"""
        try:
            ctl_content = self.template.render(
                radius=radius,
                epsilon=epsilon,
                num_bands=num_bands,
                resolution=resolution
            )
            
            with open(ctl_path, 'w') as f:
                f.write(ctl_content)
            
            logging.info(f"CTL 文件已写入: {ctl_path}")
            return True
            
        except Exception as e:
            logging.error(f"写入 CTL 文件失败: {e}")
            return False
    
    def run_mpb(self, ctl_path, output_path):
        """运行 MPB 计算并将输出保存到文件"""
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['OMP_NUM_THREADS'] = Config.OMP_NUM_THREADS
            
            # 运行 MPB 并将输出重定向到文件
            with open(output_path, 'w') as output_file:
                proc = subprocess.run(
                    ['mpb', ctl_path],
                    cwd=Config.RESULT_FOLDER,
                    env=env,
                    text=True,
                    stdout=output_file,
                    stderr=subprocess.PIPE,
                    timeout=Config.MPB_TIMEOUT
                )
            
            if proc.returncode != 0:
                logging.error(f"MPB 计算失败: {proc.stderr.strip()}")
                return False
            
            logging.info(f"MPB 计算成功，输出保存到: {output_path}")
            return True
            
        except subprocess.TimeoutExpired:
            logging.error("MPB 计算超时")
            return False
        except Exception as e:
            logging.error(f"运行 MPB 失败: {e}")
            return False
